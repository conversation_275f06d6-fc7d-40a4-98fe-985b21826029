/*css reset START*/
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    margin: 0;
    padding: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

html {
    position: relative;
    width: 100%;
    height: 100%;
    font-size: 100px;
}

body {
    font: .22rem/1.75 -apple-system, "source", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 0.24rem;
    position: relative;
    width: 100%;
    height: 100%;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

audio,
canvas,
video {
    display: inline-block;
    *display: inline;
    *zoom: 1;
}

fieldset,
img {
    border: 0;
}

img {
    vertical-align: top;
    /*-webkit-touch-callout: none;*/
    user-select: none;
}

address,
caption,
cite,
code,
dfn,
em,
strong,
th,
var {
    font-style: normal;
    font-weight: normal;
}

ol,
ul {
    list-style: none;
}

caption,
th {
    text-align: left;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
    font-weight: normal;
}

q:before,
q:after {
    content: '';
}

abbr,
acronym {
    border: 0;
    font-variant: normal;
}

sup {
    vertical-align: text-top;
}

sub {
    vertical-align: text-bottom;
}

input,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    *font-size: 100%;
}

.clear:after {
    content: " ";
    clear: both;
    height: 0;
    visibility: hidden;
    display: block;
}

.clear {
    *zoom: 1;
}

:focus {
    outline: 0;
}

a,
a:active {
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    -webkit-user-select: none;
    user-select: none;
    text-decoration: none;
}

.hide {
    width: 0;
    height: 0;
    overflow: hidden;
    display: none !important;
}

.t {
    display: block;
    overflow: hidden;
    text-indent: -9999px;
    font-size: 0;
}

.pr {
    position: relative;
}

.pa {
    position: absolute;
}

.pf {
    position: fixed;
}

.flex {
    display: flex;
    display: -webkit-flex;
}

.flex-1 {
    flex: 1;
    -webkit-flex: 1;
}

.flex-center {
    justify-content: center;
    -webkit-justify-content: center;
}

.flex-middle {
    align-items: center;
}

.flex-between {
    justify-content: space-between;
    -webkit-justify-content: space-between;
}

.flex-cm {
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
}

.t-c {
    text-align: center;
}

.t-l {
    text-align: left;
}

.t-r {
    text-align: right;
}

.bg {
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100% auto;
}

.bg-full {
    background-size: 100% 100%;
}

.full-img img {
    width: 100%;
    display: block;
}

input[type=tel]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: textfield;
}

.btn_click {
    pointer-events: auto;
}

/*** reset end ***/

/*** page ***/
.y {
    color: #fff554;
    font-weight: normal;
}

.red {
    color: red !important;
}

.t-cs {
    color: #ffa201;
}

body {
    background-color: #dddddd;
    box-sizing: border-box;
    overflow-x: hidden;
}

#afooter {
    text-align: center;
    color: #072878;
    font-size: .18rem;
    padding: .1rem 0;
}

.page {
    background: url("../ossweb-img/bg.jpg") no-repeat 0 0 / 100% auto;
    width: 100%;
    min-height: 16.24rem;
    position: relative;
    color: #0a2e87;
    font-size: .24rem;
    box-sizing: border-box;
}

.head,
.page-con {
    width: 100%;
    position: relative;
}

.head {
    height: 5.8rem;
}

.btn-top {
    top: 1.1rem;
    position: absolute;
    width: .5rem;
    height: .5rem;
    z-index: 9;
}

.btn-back {
    left: .26rem;
    background-image: url("../ossweb-img/icon-back.png");
}

.btn-rule {
    right: 1.06rem;
    background-image: url("../ossweb-img/icon-rule.png");
}

.btn-share {
    right: .26rem;
    background-image: url("../ossweb-img/icon-gift.png");
}

.btn-record {
    background-image: url("../ossweb-img/btn-record.png");
    width: 1.26rem;
    height: .4rem;
    position: absolute;
    right: .06rem;
    top: .84rem;
}

.loginfo {
    background: url("../ossweb-img/login-bg2.png") no-repeat 0 0 / 100% auto;
    width: 6.14rem;
    height: .54rem;
    line-height: .54rem;
    position: absolute;
    top: 4.7rem;
    left: 50%;
    margin-left: -3.07rem;
    text-align: center;
    font-size: .24rem;

    color: #072878;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loginfo-box {
    display: flex;
    justify-content: center;
    align-items: center;
}

.loginfo .areaName {
    max-width: 3.2rem;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.loginfo a {
    text-decoration: underline;
    color: #072878;
}

.btn-change {
    background: url("../ossweb-img/icon-change.png") no-repeat 0 0 / 100% auto;
    width: .28rem;
    height: .28rem;
    font-size: 0;
    text-indent: 99999em;
    display: inline-block;
}

.time {
    position: absolute;
    top: 3.86rem;
    left: .78rem;
    background-image: url("../ossweb-img/time.png");
    width: 1.5rem;
    height: .3rem;
}




/*** pop  ***/
.pop {
    display: none;
    position: relative;
    background: no-repeat 0 0 / 100% auto;
    font-size: .22rem;
    line-height: .3rem;
    color: #072878;
    box-sizing: border-box;
    width: 5.54rem;
}

/*瑕嗙洊鍘熸湁鏁堟灉pop*/
body .pop {
    font-size: .24rem;
    line-height: .3rem;
    color: #072878;
    box-shadow: none;
}

body .lay-shade {
    opacity: .7 !important;
}

.pop-close {
    background: url("../ossweb-img/pop-close.png") no-repeat 0 0 / 100% auto;
    width: .38rem;
    height: .37rem;
    position: absolute;
    top: .2rem;
    right: .23rem;
    /* margin-left: -.3rem; */
    z-index: 99;
    font-size: 0;
    text-indent: -9999em;
}

.pop-cnt,
.pop-desc {
    width: 4.57rem;
    margin: 0 auto;
}

.pop-desc {
    text-align: justify;
    margin: 0 .55rem 0 .45rem;
    font-size: .16rem;
    color: #76b4f3;
    text-align: center;
}

.pop {
    height: 3.71rem;
    background-image: url("../ossweb-img/pop.png");
}

.pop-tit {
    width: 100%;
    height: .94rem;
    margin: 0 auto;
}

.pop-com .pop-tit {
    height: 1.14rem;
}

.pop-com .pop_tip_wrap {
    height: 2.02rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: .28rem;
    line-height: .36rem;
}

.pop .red {
    color: #4b80ed;
    font-style: normal;
}


/*** .pop .btns-box ***/
.pop .btns-box {
    width: 100%;
    /*position: relative;*/
    z-index: 2;
    margin: .16rem auto 0;
    display: flex;
    justify-content: center;

    position: absolute;
    left: 0;
    bottom: .3rem;
}

.pop .btns-box a {
    width: 2.87rem;
    height: .76rem;
    background-repeat: no-repeat;
    background-size: 100% auto;
}

.pop .btns-box .btn-txdz {
    background-image: url("../ossweb-img/btn-txdz.png");
}

.pop .btns-box .btn-sharelink {
    background-image: url("../ossweb-img/btn-sharelink.png");
}

.pop .btns-box .btn-submit {
    background-image: url("../ossweb-img/btn-submit.png");
}

.pop .btns-box .btn-quer {
    background-image: url("../ossweb-img/btn-quer.png");
}


/*** pop-record ***/
.scroll {
    overflow: auto;
}

.pop-record .pop-desc {
    color: white;
    font-size: .16rem;
    line-height: .2rem;
    margin-top: .8rem;
}
.pop-record .pop-cnt{
    width: 5.2rem;
}

.pop-record .pop-tit {
    height: .8rem;
}

.scroll::-webkit-scrollbar {
    width: 0.05rem;
}

.scroll::-webkit-scrollbar-thumb {
    width: 0.05rem;
    border-radius: 0;
    background: #6587fd;
}

.scroll::-webkit-scrollbar-track {
    width: 0.02rem;
    border-radius: 0;
    background: #a4bcd7;
}
.pop-record {
    height: 7.8rem;
    background-image: url("../ossweb-img/pop2.png");
}

.pop-record .pop-cnt.scroll {
    height: 5.2rem;
    margin: .35rem auto .24rem;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: .1rem;
}


/*  .no-data */
.no-data-txt {
    display: none;
    font-size: .26rem;
    text-align: center;
    line-height: .4rem;
    margin-top: 1.8rem;
}

.record-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.no-data .record-list {
    display: none;
}

.no-data .no-data-txt {
    display: block;
}

.record-list li {
    width: 100%;
    min-height: .88rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    box-sizing: border-box;
    margin-bottom: .18rem;
    border-bottom: 1px solid #c3def4;
}
.record-avatar{
    position: relative;
    padding-right: .1rem;
}
.avatar-wrapper{
     width: 1.35rem;
     height: 1.35rem;
     background-image: url('/ossweb-img/awa-bg.png');
     background-repeat: no-repeat;
     background-size: 100% auto;
     display: flex;
     justify-content: center;
     align-items: center;
}
.record-avatar .avatar{
    width: .84rem;
    height: .84rem;
}
.record-avatar .avatar-mark{
    width: 0.28rem;
    height: auto;
    min-height: .56rem;
    max-height: .75rem;
    position: absolute;
    left:0;
    top:0;
}


.record-info {
    width: calc(100% - 1rem);
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-size: .22rem;
    line-height: .28rem;
}

.record-name {
    width: 96%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.record-time,
.record-adr {
    margin-top: .04rem;
    width: 100%;
    font-size: .18rem;
}
.record-list .btn-adrs{
    width: 1.96rem;
    height: .62rem;
}

/*.record-info .btn-shiwu {*/
/*    color: #104974;*/
/*    text-decoration: underline;*/
/*    font-size: .18rem;*/
/*    line-height: .28rem;*/
/*    position: absolute;*/
/*    right: 0;*/
/*    top: .28rem;*/
/*}*/


/*** pop-prize ***/
.pop-prize {
    height: 6.06rem;
    background: url("../ossweb-img/pop4.png") no-repeat 0 0 / 100% auto;
}

.pop-prize .pop-desc {
    min-height: 1rem;
    margin-bottom: -.1rem;
}

.pop-prize .pop-cnt {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.pop-prize .pop-cnt .pop-h {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

.pop-cnt .pop-h p {
    margin-top: .1rem;
}

.pop-h img {
    width: 1.38rem;
    height: 1.38rem;
}

.pop-prize .pop-cnt .pop-b {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: .15rem;
    margin: .35rem 0;
}

.pop-b img:first-child {
    width: 0.83rem;
    height: 0.51rem;
}

.pop-b .select-pool {
    width: 2.83rem;
    height: 0.46rem;
}

.pop-b .num {
    width: 0.64rem;
    height: 0.64rem;
    background-color: #ffce56;
    color: #a04f0d;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: .22rem;
    border-radius: 50%;
}

.pop-prize .btns-box {
    bottom: 0rem;
}

.pop-prize .btns-box .btn-accept {
    background-image: url(/ossweb-img/btn-accept.png);
}

/*** pop-address ***/
.pop-address {
    height: 7.41rem;
    background-image: url("./pop3.png");
}
.pop-address .pop-body{
    margin-top: 1.3rem;
}

/*** pop-invite ***/
.pop-invite {
    height: 5.23rem;
    background-image: url("./pop7.png");
}

.pop-invite .pop-h {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: .6rem;
    margin-top: 1rem;
}

.pop-invite .pop-h .award-item {
    width: 1.4rem;
    height: 1.68rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.pop-h .award-item .award-img1,
.pop-h .award-item .award-img2 {
    width: 1.38rem;
    height: 1.38rem;
    margin-bottom: .1rem;
}

.pop-invite .pop-b {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: .15rem;
    gap: .2rem;
}

.pop-invite .invite-friend {
    width: 2.83rem;
    height: 0.46rem;
}

.pop-invite .pop-b .invite-m,
.pop-invite .pop-b .invite-y {
    width: .64rem;
    height: 0.64rem;
    background-color: #70d0ff;
    color: #fff;
    font-size: .24rem;
    border-radius: 50%;
    text-align: center;
    line-height: .64rem;
}

.pop-invite .pop-desc {
    margin: .2rem 0 0;
}

.pop-invite .btns-box {
    bottom: -.1rem;
}

.pop-invite .btn-copy {
    background-image: url("/ossweb-img/btn-copy.png");
}
/*** pop-comass ***/
.pop-comass{
    height:5.23rem;
    background-image: url("/ossweb-img/pop9.png");
}
.pop-comass .pop-body .pop-cnt{
    display: flex; 
    justify-content: center;
    align-items: center;
}
.pop-comass .text-code{
    width: 4.5rem;
    height: 0.6rem;
    background-color: #dce7ff;
    border-radius: .15rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-left:.12rem;
    color: #335ae2;

}
.pop-comass .text-code::placeholder{
    padding-left: .12rem;
}
.pop-comass .btns-box{
    bottom:-.1rem;
}
.pop-comass .btn-qrzl{
    background-image: url("/ossweb-img/btn-qrzl.png");
}


/*** pop-assist ***/
.pop-assist {
    height: 6.06rem;
    background-image: url("./pop8.png");

}

.pop-assist .pop-h {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: .6rem;
}

.pop-assist .pop-h .award-item {
    width: 1.4rem;
    height: 1.68rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.pop-assist .pop-h .award-item .award-img1,
.pop-assist .pop-h .award-item .award-img2 {
    width: 1.38rem;
    height: 1.38rem;
    margin-bottom: .1rem;
}

.pop-assist .pop-b {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: .15rem;
}

.pop-assist .assist-friend {
    width: 2.83rem;
    height: 0.46rem;
}
.pop-assist .pop-h{
    margin-top: 1rem;
}
.pop-assist .pop-b{
    margin-top: .38rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: .2rem;
}

.pop-assist .pop-b .assist-m,
.pop-assist .pop-b .assist-y {
    width: .64rem;
    height: 0.64rem;
    background-color: #70d0ff;
    color: #fff;
    font-size: .24rem;
    border-radius: 50%;
    text-align: center;
    line-height: .64rem;
}

.pop-assist .pop-desc {
    margin: .2rem 0 0;
}

.pop-assist .btns-box {
    bottom: -.1rem;
}

.pop-assist .btn-copy {
    background-image: url("/ossweb-img/btn-copy.png");
}

/*** pop form ***/
.form_item {
    width: 100%;
    margin-bottom: .2rem;
}

.form_item:first-child {
    margin-top: .62rem;
}

.input::placeholder {
    color: #87a0f0;
}

input,
select,
textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 0;
    margin: 0;
    border: none;
    background: none;
    color: inherit;
    border-radius: 0;
    outline: none;
}

.form label {
    width: .8rem;
    text-align: left;
    color: #335ae2;
}

.form_item {
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    -webkit-justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
}

.form_item .input_wrap {
    width: calc(100% - .8rem);
}

.form_item input,
.form_item select,
.form_item textarea {
    width: 84%;
    background: #dbe7ff;
    color: #87a0f0;
    font-size: 0.16rem;
    font-weight: 400;
    height: .41rem;
    line-height: 0.41rem;
    padding: 0 .1rem;
    box-sizing: border-box;
    border-radius: 0;
}

.form_item select {
    background: url("../ossweb-img/sel-bg.png") no-repeat 0 0 / 100% auto;
    padding: 0 .6rem 0 .1rem;
}

.form_item textarea {
    height: 1.2rem;
    line-height: .36rem;
    padding: .1rem .2rem;
}

.pop-address .btn-save {
    position: absolute;
    bottom: -.6rem;
    background: url("../ossweb-img/btn-save.png") right bottom no-repeat;
    background-size: 100% auto;
}



/* pop-rule */
.pop-rule {
    height: 6.71rem;
    background-image: url("../ossweb-img/pop1.png");
}

.rule-list {
    width: 4.8rem;
    height: 5.3rem;
    margin: 1rem auto 0;
    position: relative;
    overflow: auto;
    padding-right: .15rem;
    font-size: .18rem;
}

.rule-list strong {
    font-weight: bold;
}

.rule-list p {
    margin-bottom: .1rem;
}

.rule-list p .ind {
    text-indent: -.08rem;
    display: inline-block;
}

.rule-list p span {
    color: #4b80ed;
}

.rule-list b {
    font-weight: bold;
}

.rule-list .table-wrap {
    width: 100%;
    overflow-x: auto;
    margin: 0 auto .2rem;
}

.rule-list .table-wrap table {
    width: 100%;
    border-collapse: collapse;
}

.rule-list th,
.rule-list td {
    border: .02rem solid #2a7ece;
    padding: .1rem;
    text-align: center;
    font-size: .16rem;
    line-height: .22rem;
}

.rule-list th {
    font-weight: bold;
}

/* pop-win */
.pop-win {
    height: 4.94rem;
    background-image: url("../ossweb-img/pop0.png");
}

.pop-win .pop-body {
    height: 3rem;
    margin-top: .81rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.pop-win .pop-body p:nth-child(1) {
    margin-top: .3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.pop-win .pop-body img {
    width: 1.35rem;
    height: 1.35rem;
}

.pop-win .pop-body p:nth-child(2) {
    width: 3.89rem;
    margin-top: .2rem;
}

.pop-win .btn-adr {
    width: 1.96rem !important;
    height: .62rem !important;
    position: absolute;
    bottom: -.6rem;
    background-image: url("../ossweb-img/btn-adr.png");
}


/* pop-ticket */
.pop-ticket {
    height: 5.2rem;
    background-image: url("../ossweb-img/pop5.png");
}

.pop-ticket .ticket {
    margin-top: 1rem;
    width: 2.07rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.pop-ticket .pop-cnt {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: .5rem;
}

.pop-ticket .ticket .ticket-img {
    width: 1.35rem;
    height: 1.35rem;
    margin-bottom: .1rem;
}

.pop-ticket .pop-desc {
    width: 5.1rem;
    margin: .84rem 0 0;
}

.pop-ticket .btns-box {
    bottom: -.9rem;
}

.pop-ticket .btns-box .btn-ok {
    background-image: url('/ossweb-img//btn-ok.png');
}